<template>
    <view class="index2">
        <view class="back-img w100 dfc">
<!--            <image class="w100" src="@/static/myImg/wodebeijing.png"></image>-->
            <image class="w100" src="https://video.xiyuns.cn/20250217153346.png"></image>
        </view>
        <view class="mine-title w100 dfc" :style="{ height: `${CustomBar}px`, paddingTop: `${StatusBar}px` }">
            <text class="fc1">我的</text>
        </view>
        <view class="mine-info w100 df flr jc-sb alc">
            <view class="user-info df flr jc-fs alc">
                <view class="user-pic dfc" @click="jumpPage(1)">
                    <image :src="userInfo.headimgUrl" v-if="userInfo && userInfo.headimgUrl"></image>
                    <image src="@/static/myImg/renwutouxiang.png" v-else></image>
                </view>
                <view class="info-con df flc jc-fs als">
                    <view class="info-name dfc">
                        <text class="fc1">{{ userInfo.nickName }}</text>
                    </view>
                    <view class="info-rank df flr jc-fs alc">
                        <view class="rank-img dfc">
                            <image src="@/static/myImg/huiyuan.png"></image>
                        </view>
                        <view class="rank-text dfc">
                            <text class="fc1">普通会员</text>
                        </view>
                    </view>
                    <view class="info-id dfc">
                        <text class="fc1">用户ID：{{ userInfo.id }}</text>
                    </view>
                </view>
            </view>
            <view class="info-set-con df flc jc-fs alc">
                <view class="set-top-con df flr jc-fs alc">
                    <view class="set-img dfc" @click="jumpPage(1)">
                        <image src="@/static/myImg/shezhi.png"></image>
                    </view>
<!--                    <view class="message-icon dfc">-->
<!--                        <image src="@/static/myImg/xiaoxi.png"></image>-->
<!--                    </view>-->
                </view>
                <view class="set-bot-con df flr jc-fs alc" @click="jumpPage(2)">
                    <view class="sign-in-pic dfc">
                        <image src="@/static/myImg/qiandao.png"></image>
                    </view>
                    <view class="sign-in-text dfc">
                        <text class="fc1">签到</text>
                    </view>
                </view>
            </view>
        </view>
        <BaseInfoShow type="1" :data="amountShow" @clickInfo="clickInfo"></BaseInfoShow>
        <view style="margin-top: 40rpx;">
            <BaseInfoShow type="2" title="我的订单" :data="orderData" @clickInfo="clickInfo"></BaseInfoShow>
        </view>
        <view style="margin-top: 16rpx;">
            <BaseInfoShow type="4" title="我的服务" :data="healthServicesData" imgSize="74rpx" @clickInfo="clickInfoService"></BaseInfoShow>
        </view>
        <view style="margin-top: 16rpx;">
            <BaseInfoShow type="3" title="我的工具" :data="myToolsData" imgSize="50rpx" @clickInfo="clickInfo"></BaseInfoShow>
        </view>
        <view class="like-box w100 df flc jc-fs alc">
            <view class="box-title w100 dfc">
                <view class="text-line"></view>
                <view class="text-con dfc">
                    <text class="fc5 fwb">猜你喜欢</text>
                </view>
                <view class="text-line"></view>
            </view>
            <goods-card :goodsList="shopList"></goods-card>
<!--            <scroll-view class="scroll-view-box">-->
<!--                <view class="box-list w100">-->
<!--                    <block v-for="(item,index) in shopList" :key="index">-->
<!--                        <view class="box-item w100 df flc jc-fs alc">-->
<!--                            <view class="item-pic dfc w100 bc1">-->
<!--                                <image class="w100" :src="item['picUrls'][0]"></image>-->
<!--                            </view>-->
<!--                            <view class="item-info-box w100 df flc jc-fs alc bc1">-->
<!--                                <view class="info-title w100 df flr jc-fs alc">-->
<!--                                    <text class="fwb fc5 single-line-hide">{{ item.name }}</text>-->
<!--                                </view>-->
<!--                                <view class="info-price-box w100 df flr jc-sb alc">-->
<!--                                    <view class="price-text df flc jc-fs als">-->
<!--                                        <view class="text-one dfc">-->
<!--                                            <text>￥-->
<!--                                                <text>{{ item['priceUp'] }}</text>-->
<!--                                            </text>-->
<!--                                        </view>-->
<!--                                        <view class="text-two dfc">-->
<!--                                            <text class="fc6">已售{{item['saleNum']}}</text>-->
<!--                                        </view>-->
<!--                                    </view>-->
<!--                                    <view class="price-img dfc">-->
<!--                                        <image src="@/static/myImg/gouwuche.png"></image>-->
<!--                                    </view>-->
<!--                                </view>-->
<!--                            </view>-->
<!--                        </view>-->
<!--                    </block>-->
<!--                </view>-->
<!--            </scroll-view>-->
        </view>
    </view>
</template>

<script name="index2">
import BaseInfoShow from "@/components/BaseInfoShow/BaseInfoShow.vue";

const app = getApp();
import api from 'utils/api'
import util from 'utils/util'
import JimUtil from '@/utils/jim-util' // IM工具库
import __config from '@/config/env'; // 配置文件Ω
import packagejson from '@/package.json'
import {go} from "@/utils/customUtil";
import {deepCopy} from "@/utils/customUtil";
import goodsCard from "@/components/goods-card/index.vue";

export default {
    components: {
        goodsCard,
        BaseInfoShow,
    },
    data() {
        return {
            amountShow: [
                {
                    label: "余额",
                    value: 0
                },
                // {
                //     label: "佣金",
                //     value: 0,
                //     pathUrl: "/pageA/distribution/distribution-center/index"
                // },
                {
                    label: "优惠券",
                    value: 0,
                    type: "couponNum",
                    pathUrl: "/extraJumpPages/coupon/coupon-user-list/index"
                },
                {
                    label: "积分",
                    value: 0,
                    type: "pointsCurrent",
                    pathUrl: "/pages/user/user-points-record/index"
                }
            ],
            orderData: [
                {
                    label: "待付款",
                    value: 0,
                    isShow: false,
                    imageUrl: "myImg/daifukuan.png",
                    pathUrl: "/pages/order/order-list/index?status=0"
                },
                {
                    label: "待发货",
                    value: 0,
                    isShow: false,
                    imageUrl: "myImg/daifahuo.png",
                    pathUrl: "/pages/order/order-list/index?status=1"
                },
                {
                    label: "待收货",
                    value: 0,
                    isShow: false,
                    imageUrl: "myImg/daishouhuo.png",
                    pathUrl: "/pages/order/order-list/index?status=2"
                },
                {
                    label: "已完成",
                    value: 0,
                    isShow: false,
                    imageUrl: "myImg/daipingjia.png",
                    pathUrl: "/pages/order/order-list/index?status=4"
                },
                {
                    label: "全部订单",
                    value: 0,
                    isShow: false,
                    imageUrl: "myImg/quanbudingdan.png",
                    pathUrl: "/pages/order/order-list/index"
                },
                {
                    label: "历史",
                    value: 0,
                    isShow: false,
                    imageUrl: "myImg/quanbudingdan.png",
                    pathUrl: "/pageA/histroyOrder/index"
                }
            ],
            healthServicesData: [
                {
                    label: "慢病管理",
                    value: 0,
                    imageUrl: "myImg/manbingguanli.png",
                    appid:'wx753f7569ed1b8191'
                },
                {
                    label: "挂号服务",
                    value: 0,
                    imageUrl: "myImg/guahaofuwu.png",
                    appid: 'wx9c0dd0368b995e9f',
                    pathText: '/pages/webview/webview/?weburl=https://app.91taogu.com/wechat/module/project/czzxyyhlwyy/hospital/departmentList/?type=1&source=&appid=548',
                    shortLink: '#小程序://沧州市中心医院智慧医院/SFH9yoiDtefyN4I'
                },
                {
                    label: "检测检验",
                    value: 0,
                    imageUrl: "myImg/jiancejianyan.png",
                    appid: 'wx9c0dd0368b995e9f',
                    pathText: '/pages/outpatientReport/outpatientReport'
                },
                {
                    label: "互联网医院",
                    value: 0,
                    imageUrl: "myImg/hulianwangyiyuan.png",
                    appid: 'wx9c0dd0368b995e9f'
                },
            ],
            myToolsData: [
                {
                    label: "我的收藏",
                    value: 0,
                    imageUrl: "myImg/wodeshoucang.png",
                    pathUrl: "/pages/user/user-collect/index"
                },
                {
                    label: "我的团购",
                    value: 0,
                    imageUrl: "myImg/wodetuangou.png",
                    pathUrl: "/pageA/groupon/groupon-user-list/index"
                },
                // {
                //     label: "我的砍价",
                //     value: 0,
                //     imageUrl: "myImg/wodekanjia.png",
                //     pathUrl: "/pageA/bargain/bargain-user-list/index"
                // },
                // {
                //     label: "我的秒杀",
                //     value: 0,
                //     imageUrl: "myImg/wodemiaosha.png",
                //     pathUrl: "/pageA/seckill/seckill-user-list/index"
                // },
                // {
                //     label: "我的签到",
                //     value: 0,
                //     imageUrl: "myImg/wodeqiandao.png",
                //     pathUrl: "/extraJumpPages/signrecord/signrecord-info/index"
                // },
                {
                    label: "我的优惠券",
                    value: 0,
                    // imageUrl: "myImg/huiyuan.png",
                    pathUrl: "/extraJumpPages/coupon/coupon-user-list/index",
                    icon: "icon-youhuiquan"
                },
                {
                    label: "收货地址",
                    value: 0,
                    imageUrl: "myImg/daishouhuo.png",
                    pathUrl: "/pages/user/user-address/list/index"
                },
                {
                    label: "接单大厅",
                    value: 0,
                    imageUrl: "myImg/maker.png",
                    pathUrl: "/subPageFlow/pages/flowIndex/index"
                },
                {
                    label: "店铺入驻",
                    value: 0,
                    imageUrl: "public/img/user-center/user-shop-apply.png",
                    pathUrl: "/extraJumpPages/shop/shop-apply/index",
                    isShow: false
                }
            ],

            CustomBar: this.CustomBar,
            StatusBar: this.StatusBar,
            theme: app.globalData.theme, //全局颜色变量
            userInfo: {},
            distributionConfig: null,
            orderCountAll: [],
            showPrivacyPolicy: __config.showPrivacyPolicy,
            privacyPolicyUrl: __config.privacyPolicyUrl,
            protocolUrl: __config.protocolUrl,
            canIUseGetUserProfile: false,
            version: packagejson.version,
            isWeiXinBrowser: util.isWeiXinBrowser(),

            shopList: []
        }
    },
    onLoad(option) {
        // #ifdef MP-WEIXIN
        if (uni.getUserProfile) {
            this.canIUseGetUserProfile = true
        }
        // #endif
        // #ifdef H5
        let code = option.code;
        let state = option.state;
        //授权code获取用户信息
        if (code && state == 'snsapi_userinfo_update') { //有code
            this.userInfoUpdateByMp({
                jsCode: code,
                scope: state
            });
        }
        // #endif
    },
    onShow() {
        app.initPage().then(res => {
            this.getInfoCon()
            this.userInfoGet();
            this.orderCountAllFun();
        });
        // 更新消息消息未读数
        JimUtil.getJIMUnreadMsgCnt()
        //更新购物车tabar角标数量
        uni.setTabBarBadge({
            index: 3,
            text: app.globalData.shoppingCartCount + ''
        });
        // #ifdef MP-WEIXIN
        // app.doLogin()
        // #endif
    },
    methods: {
        /**
         * 设置按钮、头像：pages/user/user-info/index
         * 签到按钮：pages/signrecord/signrecord-info/index
         * 积分：pages/user/user-points-record/index
         * 优惠卷：pages/coupon/coupon-user-list/index
         * 佣金：pages/distribution/distribution-center/index
         * */
        // 跳转页面
        jumpPage(data) {
            if(data === 1) {
                go(`/pages/user/user-info/index`)
            } else if(data === 2) {
                go(`/extraJumpPages/signrecord/signrecord-info/index`)
            }
        },
        // 点击获取详细信息
        clickInfo(data) {
            if(data && data.pathUrl) {
                go(data.pathUrl)
            }
        },
        // 点击健康服务
        clickInfoService(data) {
            console.log(data, 'data')
            uni.navigateToMiniProgram({
                appId: data['appid'],
                path: data['pathText'],
                envVersion: 'release',
                shortLink: data['shortLink'] || undefined,
                complete(res) {
                    // 打开成功
                    console.log('打开信息：', res)
                }
            })
        },
        // 获取信息
        async getInfoCon() {
            let {code, data} = await api.pagedevise(1)
            let that = this;
            if (code === 0) {
                data.pageComponent.componentsList.map(item => {
                    if (item.componentName === 'goodsComponent') {
                        api.goodsSpuListByIds(item.data.goodsIds).then(res => {
                            that.shopList = res.data
                        })
                    }
                })
            }
        },

        goPage(path) {
            if (path) {
                uni.navigateTo({
                    url: path
                })
            }
        },
        // #ifdef MP-WEIXIN
        agreeGetUser(e) {
            if (e.detail.errMsg == 'getUserInfo:ok') {
                api.userInfoUpdateByMa(e.detail).then(res => {
                    this.userInfo = res.data;
                    uni.setStorageSync('user_info', this.userInfo);
                    this.userInfoGet();
                });
            }
        },

        getUserProfile(e) {
            // 该接口2022年10月25日后收回 https://developers.weixin.qq.com/community/develop/doc/00022c683e8a80b29bed2142b56c01
            // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
            // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
            wx.getUserProfile({
                desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
                success: (detail) => {
                    api.userInfoUpdateByMa(detail).then(res => {
                        this.userInfo = res.data;
                        uni.setStorageSync('user_info', this.userInfo);
                        this.userInfoGet();
                    });
                }
            })
        },
        // #endif
        // #ifdef H5
        updateUserInfo() {
            if (util.isWeiXinBrowser()) {
                //微信公众号H5，页面授权获取用户详情信息
                let appId = app.globalData.appId;
                let pages = getCurrentPages();
                let currentPage = pages[pages.length - 1];
                let route = currentPage.route;
                let redirectUri = location.href;
                let componentAppId_str = app.globalData.componentAppId ? '&component_appid=' + app.globalData
                        .componentAppId : '';
                redirectUri = encodeURIComponent(redirectUri);
                let wx_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId +
                        '&redirect_uri=' + redirectUri + componentAppId_str +
                        '&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo_update#wechat_redirect';
                location.href = wx_url;
            }
        },
        //通过微信公众号网页授权更新用户信息
        userInfoUpdateByMp(parm) {
            let that = this;
            api.userInfoUpdateByMp(parm).then(res => {
                //公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
                let query = that.$Route.query;
                delete query.code;
                delete query.state;
                util.resetPageUrl(query);
                this.userInfo = res.data;
                uni.setStorageSync('user_info', this.userInfo);
                this.userInfoGet();
            }).catch(res => {

            });
        },
        // #endif
        //获取商城用户信息
        userInfoGet() {
            api.userInfoGet().then(res => {
                let amountShowCopy = deepCopy(this.amountShow)
			    uni.setStorageSync('user_info', res.data);
                amountShowCopy.map(item=>{
                    item['value'] = item['type']?res.data[item['type']]:0
                })
                this.amountShow = amountShowCopy
                this.userInfo = res.data;
                this.myToolsData.map(item=>{
                    item['isShow'] = this.userInfo?.nickName ? true : false
                })
            });
            //分销设置
            api.distributionConfig().then(res => {
                this.distributionConfig = res.data
            });
        },

        orderCountAllFun() {
            api.orderCountAll().then(res => {
                let orderDataCopy = deepCopy(this.orderData)
                orderDataCopy.map((item, index)=>{
                    item['value'] = res.data[index]?res.data[index]:0
                    item['isShow'] = item['value'] !== 0;
                })
                this.orderData = orderDataCopy
                this.orderCountAll = res.data;
            });
        },

        logout() {
            uni.showModal({
                content: '确定删除账号吗？',
                cancelText: '我再想想',
                confirmColor: '#ff0000',
                success(res) {
                    if (res.confirm) {
                        api.logout().then(res => {
                            let userInfo = res.data;
                            uni.setStorageSync('user_info', userInfo);
                            if (userInfo) {
                                uni.setStorageSync('third_session', userInfo.thirdSession);
                            }
                            //登出IM
                            JimUtil.IMloginOut()
                            //退出登录完成跳到首页
                            uni.reLaunch({
                                url: '/pages/home/<USER>'
                            });
                            //清空购物车数量
                            uni.setTabBarBadge({
                                index: 3,
                                text: '0'
                            });
                        });
                    }
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.index2 {
    .back-img {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;

        image {
            height: 750rpx;
        }
    }

    .mine-title {
        margin-bottom: 65rpx;

        text {
            font-size: 40rpx;
            font-weight: 500;
        }
    }

    .mine-info {
        padding: 0 30rpx 60rpx 40rpx;

        .user-info {
            .user-pic {
                padding-right: 19rpx;

                image {
                    width: 138rpx;
                    height: 138rpx;
                    border-radius: 50%;
                }
            }

            .info-con {
                .info-name {
                    text {
                        font-size: 40rpx;
                        font-weight: 800;
                    }
                }

                .info-rank {
                    margin: 19rpx 0 13rpx 0;

                    .rank-img {
                        padding-right: 2rpx;

                        image {
                            width: 40rpx;
                            height: 37rpx;
                        }
                    }

                    .rank-text {
                        text {
                            font-size: 26rpx;
                            font-weight: 500;
                        }
                    }
                }

                .info-id {
                    text {
                        font-size: 26rpx;
                        font-weight: 500;
                    }
                }
            }
        }

        .info-set-con {
            .set-top-con {
                margin-bottom: 46rpx;

                .set-img {
                    padding-right: 40rpx;

                    image {
                        width: 40rpx;
                        height: 38rpx;
                    }
                }

                .message-icon {
                    image {
                        width: 40rpx;
                        height: 38rpx;
                    }
                }
            }

            .set-bot-con {
                border: 2rpx solid #FFFFFF;
                border-radius: 25rpx;
                padding: 7rpx 21rpx;

                .sign-in-pic {
                    padding-right: 10rpx;

                    image {
                        width: 25rpx;
                        height: 24rpx;
                    }
                }

                .sign-in-text {
                    text {
                        font-size: 26rpx;
                        font-weight: 500;
                    }
                }
            }
        }
    }

    .like-box {
        .box-title {
            padding: 30rpx 0;

            .text-line {
                width: 80rpx;
                height: 1rpx;
                background-color: #DCDCDC;
            }

            .text-con {
                margin: 0 20rpx;

                text {
                    font-size: 30rpx;
                }
            }
        }

        .scroll-view-box {
            .box-list {
                padding: 30rpx 20rpx;
                display: grid;
                grid-gap: 18rpx;
                grid-template-columns: repeat(2, 1fr);

                .box-item {
                    border-radius: 20rpx;

                    .item-pic {
                        border-top-left-radius: 20rpx;
                        border-top-right-radius: 20rpx;
                        image {
                            height: 346rpx;
                            border-top-left-radius: 20rpx;
                            border-top-right-radius: 20rpx;
                        }
                    }

                    .item-info-box {
                        padding: 23rpx 20rpx 30rpx 20rpx;
                        border-bottom-left-radius: 20rpx;
                        border-bottom-right-radius: 20rpx;

                        .info-title {
                            margin-bottom: 29rpx;

                            text {
                                font-size: 32rpx;
                            }
                        }

                        .info-price-box {
                            .price-text {
                                .text-one {
                                    margin-bottom: 20rpx;

                                    text {
                                        color: #FF6203;
                                        font-size: 24rpx;
                                        font-weight: 500;

                                        text {
                                            font-size: 36rpx;
                                            font-weight: 500;
                                        }
                                    }
                                }
                            }

                            .price-img {
                                image {
                                    width: 60rpx;
                                    height: 60rpx;
                                    border-radius: 50%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>