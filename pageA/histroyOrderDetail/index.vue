<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">历史订单详情</block>
		</cu-custom>
    <view class="history-detail-container">

    </view>
	</view>
</template>

<script>
export default {
  data() {
    return {
      theme: getApp().globalData.theme, //全局颜色变量
			CustomBar: this.CustomBar,
    };
  },
};
</script>

<style scoped lang="scss">

</style>
