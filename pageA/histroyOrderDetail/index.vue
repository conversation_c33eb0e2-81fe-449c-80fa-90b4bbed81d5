<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">历史订单详情</block>
		</cu-custom>

		<view class="history-detail-container">
			<!-- 收货地址信息 -->
			<view class="address-card">
				<view class="address-header">
					<image class="location-icon" src="/static/myImg/maker.png" mode="aspectFit"></image>
					<view class="contact-info">
						<text class="contact-name">{{ itemDetail.linkman }}</text>
						<text class="contact-phone">{{ itemDetail.tel }}</text>
					</view>
				</view>
				<view class="address-detail">
					<text class="address-label">地址：</text>
					<text class="address-text">{{ getFullAddress() }}</text>
				</view>
			</view>

			<!-- 商品信息卡片 -->
			<view class="product-card">
				<view class="product-header">
					<view class="store-icon"></view>
					<text class="store-name">{{ itemDetail.bname || '慢病服务包' }}</text>
				</view>

				<view class="product-info">
					<image class="product-image" :src="getImageSrc()" mode="aspectFit" @error="handleImageError"></image>
					<view class="product-details">
						<text class="product-name">{{ itemDetail.name }}</text>
						<text class="product-spec">{{ itemDetail.ggname }}</text>
						<view class="price-quantity">
							<text class="price">¥{{ itemDetail.sellPrice }}</text>
							<text class="quantity">×{{ itemDetail.num }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 订单信息 -->
			<view class="order-info-section">
				<view class="info-row">
					<text class="label">订单编号</text>
					<text class="value">{{ itemDetail.ordernum }}</text>
				</view>
				<view class="info-row">
					<text class="label">下单时间</text>
					<text class="value">{{ formatTime(itemDetail.createtime) }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.status >= 1">
					<text class="label">支付时间</text>
					<text class="value">{{ formatTime(itemDetail.createtime) }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.status >= 1">
					<text class="label">支付方式</text>
					<text class="value">微信支付</text>
				</view>
				<view class="info-row" v-if="itemDetail.status >= 2">
					<text class="label">发货时间</text>
					<text class="value">{{ formatTime(itemDetail.createtime) }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.status >= 3 && itemDetail.endtime">
					<text class="label">收货时间</text>
					<text class="value">{{ formatTime(itemDetail.endtime) }}</text>
				</view>
			</view>

			<!-- 费用明细 -->
			<view class="cost-section">
				<view class="info-row">
					<text class="label">商品金额</text>
					<text class="value price-text">¥{{ itemDetail.totalprice }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.scoredkMoney > 0">
					<text class="label">积分抵扣</text>
					<text class="value">-¥{{ itemDetail.scoredkMoney }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.leveldkMoney > 0">
					<text class="label">会员抵扣</text>
					<text class="value">-¥{{ itemDetail.leveldkMoney }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.manjianMoney > 0">
					<text class="label">满减优惠</text>
					<text class="value">-¥{{ itemDetail.manjianMoney }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.couponMoney > 0">
					<text class="label">优惠券</text>
					<text class="value">-¥{{ itemDetail.couponMoney }}</text>
				</view>
				<view class="info-row">
					<text class="label">配送方式</text>
					<text class="value">同城配送(3元)</text>
				</view>
				<view class="info-row">
					<text class="label">配送时间</text>
					<text class="value">2025-05-20 18:00-18:30</text>
				</view>
				<view class="info-row total-row">
					<text class="label">实付款</text>
					<text class="value price-text">¥{{ itemDetail.realTotalprice }}</text>
				</view>
			</view>

			<!-- 订单状态 -->
			<view class="status-section">
				<view class="info-row">
					<text class="label">订单状态</text>
					<text class="value">{{ getOrderStatus(itemDetail.status) }}</text>
				</view>
				<view class="info-row" v-if="itemDetail.refundMoney > 0">
					<text class="label">已退款</text>
					<text class="value refund-text">¥{{ itemDetail.refundMoney }} ></text>
				</view>
				<view class="info-row" v-if="itemDetail.refundMoney > 0">
					<text class="label">退款状态</text>
					<text class="value refund-status">已退款</text>
				</view>
			</view>

			<!-- 底部按钮 -->
			<view class="bottom-actions">
				<view class="action-btn secondary" v-if="itemDetail.status === 4 || itemDetail.status === 5" @click="handleRefund">查看退款</view>
				<view class="action-btn primary" v-if="itemDetail.status === 3 || itemDetail.status === 5" @click="handleDeleteOrder">删除订单</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
  data() {
    return {
      theme: getApp().globalData.theme, //全局颜色变量
			CustomBar: this.CustomBar,
			itemDetail: {},
			defaultImage: '/static/public/img/no_pic.png', // 默认图片
			imageLoadError: false // 图片加载失败标记
    };
  },
  onLoad(options) {
    const itemDetail = JSON.parse(options.item);
    console.log(itemDetail);
    this.itemDetail = itemDetail;
  },
  methods: {
    // 获取完整地址
    getFullAddress() {
      let fullAddress = '';
      if (this.itemDetail.area2) {
        fullAddress = this.itemDetail.area2.replace(/,/g, '');
      }
      if (this.itemDetail.address) {
        fullAddress += this.itemDetail.address;
      }
      return fullAddress || '暂无地址信息';
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:00`;
    },

    // 获取订单状态文本
    getOrderStatus(status) {
      const statusMap = {
        0: '未付款',
        1: '已付款',
        2: '已发货',
        3: '已收货',
        4: '申请退款',
        5: '已退款'
      };
      return statusMap[status] || '未知状态';
    },

    // 处理退款
    handleRefund() {
      // 直接跳转到退款详情页面，传递当前订单信息
      // 这里可以根据订单信息构造退款数据，或者传递订单ID让详情页自己获取退款信息
      // 根据订单状态映射退款状态和文本
      const getRefundStatusInfo = (orderStatus) => {
        switch (orderStatus) {
          case 4: // 申请退款
            return {
              status: 4,
              statusText: '申核中'
            };
          case 5: // 已退款
            return {
              status: 5,
              statusText: '已退款'
            };
          default:
            return {
              status: 4,
              statusText: '申核中'
            };
        }
      };

      const statusInfo = getRefundStatusInfo(this.itemDetail.status);

      const refundData = {
        id: 1,
        storeName: this.itemDetail.bname || '慢病服务包',
        name: this.itemDetail.name || '消毒棉片',
        spec: this.itemDetail.ggname || '11盒',
        price: this.itemDetail.sellPrice || '22.00',
        quantity: this.itemDetail.num || 1,
        pic: this.itemDetail.pic,
        refundAmount: this.itemDetail.refundMoney || '25.00',
        status: statusInfo.status,
        statusText: statusInfo.statusText,
        refundId: '250520141852750617',
        orderNum: this.itemDetail.ordernum || '250520131500723878',
        applyTime: '2025-05-20 14:18:52',
        refundType: '退款',
        refundReason: '退款',
        auditNote: statusInfo.status === 5 ? '同意' : '审核中'
      };

      uni.navigateTo({
        url: '/pageA/refundDetail/index?refundData=' + encodeURIComponent(JSON.stringify(refundData))
      });
    },

    // 处理删除订单
    handleDeleteOrder() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此订单吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '订单已删除',
              icon: 'success'
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        }
      });
    },

    // 获取图片源
    getImageSrc() {
      // 如果图片已经加载失败过，直接返回默认图片
      if (this.imageLoadError) {
        return this.defaultImage;
      }
      // 如果没有图片URL，返回默认图片
      return this.itemDetail.pic || this.defaultImage;
    },

    // 处理图片加载错误
    handleImageError(e) {
      // 标记图片加载失败
      this.imageLoadError = true;
      console.log('图片加载失败:', e);
    }
  }
};
</script>

<style scoped lang="scss">
.history-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.address-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;

  .address-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .location-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }

    .contact-info {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .contact-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .contact-phone {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .address-detail {
    display: flex;
    align-items: flex-start;

    .address-label {
      font-size: 28rpx;
      color: #666;
      margin-right: 8rpx;
      flex-shrink: 0;
    }

    .address-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.4;
      flex: 1;
    }
  }
}

.product-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;

  .product-header {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .store-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
      border-radius: 50%;
      background: #ff6b35;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 16rpx;
        height: 16rpx;
        background: white;
        border-radius: 2rpx;
      }
    }

    .store-name {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .product-info {
    display: flex;
    padding: 24rpx;

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      margin-right: 24rpx;
      background: #f8f8f8;
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        line-height: 1.4;
        margin-bottom: 8rpx;
      }

      .product-spec {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 16rpx;
      }

      .price-quantity {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price {
          font-size: 32rpx;
          color: #ff4757;
          font-weight: 600;
        }

        .quantity {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }
}

.order-info-section,
.cost-section,
.status-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .label {
    font-size: 28rpx;
    color: #666;
  }

  .value {
    font-size: 28rpx;
    color: #333;
    text-align: right;
    max-width: 60%;
  }

  &.total-row {
    margin-top: 16rpx;
    padding-top: 24rpx;

    .label {
      font-weight: 600;
      color: #333;
    }
  }
}

.price-text {
  color: #ff4757 !important;
  font-weight: 600;
}

.refund-text {
  color: #ff4757 !important;
}

.refund-status {
  color: #ff4757 !important;
}

.bottom-actions {
  display: flex;
  gap: 24rpx;
  padding: 40rpx 0;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 28rpx;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;

    &.secondary {
      background: #f8f8f8;
      color: #666;
      border: 1rpx solid #666;
    }

    &.primary {
      background: #ff4757;
      color: white;
    }
  }
}
</style>
