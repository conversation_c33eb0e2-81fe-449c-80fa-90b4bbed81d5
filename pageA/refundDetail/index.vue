<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">退款详情</block>
		</cu-custom>
		
		<!-- 退款状态头部 -->
		<view class="status-header">
			<view class="status-content">
				<text class="status-title">{{ getStatusTitle() }}</text>
				<image class="status-icon" src="/static/public/img/no_pic.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 商品信息 -->
		<view class="product-card">
			<view class="card-header">
				<view class="store-info">
					<view class="store-icon"></view>
					<text class="store-name">{{ refundDetail.storeName }}</text>
				</view>
			</view>
			
			<view class="product-info">
				<image class="product-image" :src="getImageSrc()" mode="aspectFit" @error="handleImageError"></image>
				<view class="product-details">
					<text class="product-name">{{ refundDetail.name }}</text>
					<text class="product-spec">{{ refundDetail.spec }}</text>
					<view class="price-section">
						<text class="price">¥{{ refundDetail.price }}</text>
						<text class="quantity">×{{ refundDetail.quantity }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退款详情信息 -->
		<view class="detail-card">
			<view class="detail-row">
				<text class="detail-label">退货单号</text>
				<text class="detail-value">{{ refundDetail.refundId }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">订单编号</text>
				<text class="detail-value">{{ refundDetail.orderNum }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">申请时间</text>
				<text class="detail-value">{{ refundDetail.applyTime }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">类型</text>
				<text class="detail-value refund-type">{{ refundDetail.refundType }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">申请退款金额</text>
				<text class="detail-value refund-amount">¥{{ refundDetail.refundAmount }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">退款状态</text>
				<text class="detail-value" :class="getStatusClass()">{{ refundDetail.statusText }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">退款原因</text>
				<text class="detail-value">{{ refundDetail.refundReason }}</text>
			</view>
			<view class="detail-row">
				<text class="detail-label">审核备注</text>
				<text class="detail-value">{{ refundDetail.auditNote }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			theme: getApp().globalData.theme,
			CustomBar: this.CustomBar,
			defaultImage: '/static/public/img/no_pic.png',
			imageLoadError: false,
			refundDetail: {
				id: 1,
				storeName: '慢病服务包',
				name: '消毒棉片',
				spec: '11盒',
				price: '22.00',
				quantity: 1,
				pic: 'https://crm.dcydkj.com/upload/2/20230824/7e0fccb0793ff59ed14979e693a9992d.png',
				refundAmount: '25.00',
				status: '已同意',
				statusText: '已退款',
				refundId: '250520141852750617',
				orderNum: '250520131500723878',
				applyTime: '2025-05-20 14:18:52',
				refundType: '退款',
				refundReason: '退款',
				auditNote: '同意'
			}
		};
	},
	
	onLoad(options) {
		if (options.refundData) {
			try {
				this.refundDetail = JSON.parse(options.refundData);
			} catch (e) {
				console.error('解析退款数据失败:', e);
			}
		}
	},
	
	methods: {
		// 获取状态标题
		getStatusTitle() {
			switch (this.refundDetail.status) {
				case '已同意':
					return '审核通过，已退款';
				case '申核中':
					return '申请已提交，等待审核';
				case '驳回':
					return '申请被驳回';
				default:
					return '退款处理中';
			}
		},
		
		// 获取状态样式类
		getStatusClass() {
			switch (this.refundDetail.status) {
				case '已同意':
					return 'status-approved';
				case '申核中':
					return 'status-pending';
				case '驳回':
					return 'status-rejected';
				default:
					return '';
			}
		},
		
		// 获取图片源
		getImageSrc() {
			if (this.imageLoadError) {
				return this.defaultImage;
			}
			return this.refundDetail.pic || this.defaultImage;
		},
		
		// 处理图片加载错误
		handleImageError(e) {
			this.imageLoadError = true;
			console.log('图片加载失败:', e);
		}
	}
};
</script>

<style scoped lang="scss">
.status-header {
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
	padding: 40rpx 20rpx;
	color: white;
	
	.status-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.status-title {
			font-size: 32rpx;
			font-weight: 500;
		}
		
		.status-icon {
			width: 80rpx;
			height: 80rpx;
			opacity: 0.8;
		}
	}
}

.product-card {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	
	.card-header {
		padding: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.store-info {
			display: flex;
			align-items: center;
			
			.store-icon {
				width: 32rpx;
				height: 32rpx;
				background: #ff6b35;
				border-radius: 50%;
				margin-right: 16rpx;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 16rpx;
					height: 16rpx;
					background: white;
					border-radius: 2rpx;
				}
			}
			
			.store-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}
		}
	}
	
	.product-info {
		display: flex;
		padding: 24rpx;
		
		.product-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-right: 24rpx;
			background: #f8f8f8;
		}
		
		.product-details {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.product-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				line-height: 1.4;
				margin-bottom: 8rpx;
			}
			
			.product-spec {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 16rpx;
			}
			
			.price-section {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.price {
					font-size: 32rpx;
					color: #ff4757;
					font-weight: 600;
				}
				
				.quantity {
					font-size: 28rpx;
					color: #666;
				}
			}
		}
	}
}

.detail-card {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	
	.detail-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
		
		&:last-child {
			border-bottom: none;
		}
		
		.detail-label {
			font-size: 28rpx;
			color: #666;
			flex-shrink: 0;
			width: 200rpx;
		}
		
		.detail-value {
			font-size: 28rpx;
			color: #333;
			text-align: right;
			flex: 1;
			word-break: break-all;
			
			&.refund-type {
				color: #ff4757;
				font-weight: 500;
			}
			
			&.refund-amount {
				color: #ff4757;
				font-weight: 600;
				font-size: 30rpx;
			}
			
			&.status-approved {
				color: #52c41a;
				font-weight: 500;
			}
			
			&.status-pending {
				color: #faad14;
				font-weight: 500;
			}
			
			&.status-rejected {
				color: #ff4d4f;
				font-weight: 500;
			}
		}
	}
}
</style>
