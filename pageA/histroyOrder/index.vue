<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">历史订单</block>
		</cu-custom>
		<scroll-view scroll-x class="bg-white nav fixed">
			<view class="flex text-center">
				<view :class="'cu-item flex-sub ' + (index==tabCur?'cur text-'+theme.themeColor:'')" v-for="(item, index) in orderStatus"
				 :key="index" @tap="tabSelect" :data-index="index" :data-key="item.key">{{item.value}}</view>
			</view>
		</scroll-view>
		<view class="order-container">
			<view class="order-item" v-for="(item, index) in orderList" :key="index" @click="goToDetail(item)">
				<!-- 订单头部 -->
				<view class="order-header">
					<view class="store-info">
						<view class="store-icon"></view>
						<text class="store-name">{{ item.bname }}</text>
					</view>
					<text class="order-status">{{ statusText(item.status) }}</text>
				</view>

				<!-- 商品信息 -->
				<view class="product-section">
					<image class="product-image" :src="item.pic" mode="aspectFit"></image>
					<view class="product-info">
						<text class="product-name">{{ item.name }}</text>
						<text class="product-spec">{{ item.ggname }}</text>
						<view class="price-section">
							<text class="price">¥{{ item.sellPrice }}</text>
							<text class="quantity">×{{ item.num }}</text>
						</view>
					</view>
				</view>

				<!-- 订单总计 -->
				<view class="order-summary">
					<view class="summary-row">
						<text class="summary-text">共{{ item.num }}件商品 实付：¥{{ item.realTotalprice }}</text>
						<text class="refund-text" v-if="item.status == 5">已退款 ¥{{ item.refundMoney }}</text>
					</view>
				</view>
			</view>

			<view class="load-more" v-if="!loadmore">
				<text class="load-text">没有更多了</text>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import orderOperate from "components/order-operate/index";
	import { getHistoryOrderList } from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				tabCur: 0,
				orderStatus: [{
					value: '全部订单',
					key: ''
				}, {
					value: '待付款',
					key: '0'
				}, {
					value: '待发货',
					key: '1'
				}, {
					value: '待收货',
					key: '2'
				}, {
					value: '已完成',
					key: '3'
				}],
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				orderList: [],
				orderSouceList: []
			};
		},

		components: {
			orderOperate
		},
		props: {},

		onShow() {
			app.initPage().then(res => {
				this.loadmore = true;
				this.orderList = [];
				this.page.current = 1;
				this.orderPage();
			});
		},
		onLoad: function(options) {
			if (options.status || options.status == 0) {
				let that = this;
				this.parameter.status = options.status;
				this.orderStatus.forEach(function(status, index) {
					if (status.key == options.status) {
						that.tabCur = index;
					}
				});
			}
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.orderPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},

	methods: {
			// 跳转到订单详情
			goToDetail(item) {
				uni.navigateTo({
					url: '/pageA/histroyOrderDetail/index?item=' + JSON.stringify(item)
				});
			},

			statusText (status) {
				// 0未付款，1已付款，2已发货，3已收货，4申请退款，5已退款
				switch (Number(status)) {	
					case 0:
						return '未付款'
					case 1:
						return '已付款'
					case 2:
						return '已发货'
					case 3:
						return '已收货'
					case 4:
						return '申请退款'
					case 5:
						return '已退款'
					default:
						return '未知'
				}
			},
			orderPage() {
				// api.orderPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
				// 	let orderList = res.data.records;
				// 	this.orderList = [...this.orderList, ...orderList];
				// 	if (orderList.length < this.page.size) {
				// 		this.loadmore = false;
				// 	}
				// });
				// uni.getStorageSync('user_info').realPhone ||
				getHistoryOrderList({
					mobile:  '17531708378'
				}).then(res => {
					console.log(res, "历史订单");
					// this.orderList = [...this.orderList, ...res.data]
					this.orderList = res.data
					this.orderSouceList = res.data
					// if (res.data.length < this.page.size) {
						this.loadmore = false;
					// }
				})
			},

			refresh() {
				this.loadmore = true;
				this.orderList = [];
				this.page.current = 1;
				this.orderPage();
			},

		tabSelect (e) {
			let dataset = e.currentTarget.dataset;
			if (dataset.index != this.tabCur) {
				this.tabCur = dataset.index;
				this.parameter.status = dataset.key;
				// this.refresh();
				// 筛选
				if (dataset.key == '') {
					this.orderList = this.orderSouceList
				} else {
					this.orderList = this.orderSouceList.filter(item => item.status == dataset.key)
				}
			}
		},

			orderCancel(item, index) {
				api.orderGet(this.orderList[index].id).then(res => {
					this.orderList[index] = res.data;
					this.orderList.splice(); //当页面不渲染时调用此方法可以刷新页面
				});

			},

			orderDel(item, index) {
				this.orderList.splice(index, 1);
			},

			unifiedOrder(item, index) {
				let orderList = this.orderList;
				api.orderGet(orderList[index].id).then(res => {
					this.orderList[index] = res.data;
					this.orderList = this.orderList;
				});
			}

		}
	};
</script>
<style scoped lang="scss">
.nav {
	top: unset !important;
}

.order-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-top: 1;
}

.order-item {
	background: white;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.store-info {
			display: flex;
			align-items: center;

			.store-icon {
				width: 32rpx;
				height: 32rpx;
				background: #ff6b35;
				border-radius: 50%;
				margin-right: 16rpx;
				position: relative;

				&::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 16rpx;
					height: 16rpx;
					background: white;
					border-radius: 2rpx;
				}
			}

			.store-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}
		}

		.order-status {
			font-size: 26rpx;
			color: #ff4757;
			font-weight: 500;
		}
	}

	.product-section {
		display: flex;
		padding: 24rpx;

		.product-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-right: 24rpx;
			background: #f8f8f8;
		}

		.product-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.product-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				line-height: 1.4;
				margin-bottom: 8rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				overflow: hidden;
			}

			.product-spec {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 16rpx;
			}

			.price-section {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.price {
					font-size: 32rpx;
					color: #ff4757;
					font-weight: 600;
				}

				.quantity {
					font-size: 28rpx;
					color: #666;
				}
			}
		}
	}

	.order-summary {
		padding: 24rpx;
		border-top: 1rpx solid #f0f0f0;
		background: #fafafa;

		.summary-row {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.summary-text {
				font-size: 26rpx;
				color: #333;
			}

			.refund-text {
				font-size: 26rpx;
				color: #ff4757;
				font-weight: 500;
			}
		}
	}
}

.load-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx 0;

	.load-text {
		font-size: 24rpx;
		color: #999;
	}
}
</style>
